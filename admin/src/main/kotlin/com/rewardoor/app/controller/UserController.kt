package com.rewardoor.app.controller

import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.model.User
import org.springframework.security.access.annotation.Secured
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RestController

@RestController
class UserController(
    private val userService: UserService,
    private val projectService: ProjectService,
    private val userTwitterService: UserTwitterService,
    private val telegramService: TelegramService,
    private val dcService: DiscordService,
    private val adminService: AdminService,
    private val projectApplyService: ProjectApplyService,
    private val userInfoService: UserInfoService
) {

    @GetMapping(path = ["/user/info", "/info"])
    @Secured
    fun getTIPRoles(): UserInfoDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userDto = getUserInfoDto(user)
        userDto.canCreateProject = projectApplyService.getUserPrivilege(user.userId)
        return userDto
    }

    @GetMapping(path = ["/user/info", "/ownerInfo"])
    @Secured
    fun getOwnerInfo(): UserInfoDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userDto = getUserInfoDto(user)
        userDto.canCreateProject = projectApplyService.getUserPrivilege(user.userId)
        return userDto
    }

    @PostMapping("markNewUser")
    fun markNewUser(): UserInfoDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        userService.updateNewUserStatus(user.userId, false)
        return getUserInfoDto(userService.getUserById(user.userId)!!)
    }

    private fun getUserInfoDto(user: User): UserInfoDto {
        val projects = projectService.getUserProjects(user.userId).toMutableList()
        if (projects.isEmpty()) {  // project admin
            val address = if (user.wallet != "") user.wallet else user.ton.hexAddress ?: ""
            if (address.isNotEmpty()) {
                var admin = adminService.getAdminByWallet(address)
                if (admin != null && admin.status == 1) { //admin is valid
                    val project = projectService.getProject(admin.projectId)
                    if (project != null) {
                        projects.add(project)
                    }
                }
            }
        }
        // 获取当前 token 的 chain 字段，并对项目列表做过滤
        val authentication = SecurityContextHolder.getContext().authentication
        val chain = (authentication as? com.rewardoor.app.auth.AddressAuthentication)?.chain
        val filteredProjects = if (!chain.isNullOrBlank()) {
            projects.filter { it.chain == chain }
        } else {
            projects
        }
        val userInfoDto = userInfoService.getUserDtoInfo(user)
        userInfoDto.projects = filteredProjects.toMutableList()
        return userInfoDto
    }
}