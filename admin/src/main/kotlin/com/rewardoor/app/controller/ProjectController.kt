package com.rewardoor.app.controller

import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dao.SBTRewardRepository
import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.app.utils.TonAddressUtils
import com.rewardoor.model.*
import com.rewardoor.model.CampaignStats
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.net.URI
import java.time.LocalDateTime
import java.util.*
import kotlin.random.Random

@RestController
class ProjectController(
    val projectService: ProjectService,
    val campaignService: CampaignService,
    val credentialService: CredentialService,
    val resultStatsService: ResultStatsService,
    val userService: UserService,
    val projectApplyService: ProjectApplyService,
    val userTwitterService: UserTwitterService,
    val dcService: DiscordService,
    val telegramService: TelegramService,
    val adminService: AdminService,
    private val redisTemplate: StringRedisTemplate,
    val sbtRewardRepo: SBTRewardRepository,
    @Value("\${home.admins}") val adminAddress: MutableList<String>,
    private val participantRepo: ParticipantRepository
) {

    @PostMapping("/project")
    fun createProject(@RequestBody project: Project): SimpleResponseEntity<Project> {
        val authentication = SecurityContextHolder.getContext().authentication
        val principal = authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val chain = (authentication as? com.rewardoor.app.auth.AddressAuthentication)?.chain?.lowercase()
        if (!chain.isNullOrBlank()) {
            project.chain = chain
        }
        //        if (projectApplyService.getUserPrivilege(user.userId).not()) {
        //            return SimpleResponseEntity.failed("You are not allowed to create a project")
        //        }

        val isUrlExist = projectService.getProjectByUrl(project.projectUrl) != null
        val isNameExist = projectService.getProjectByName(project.projectName) != null
        if (isUrlExist) {
            return SimpleResponseEntity.failed("The project URL has been used")
        } else if (isNameExist) {
            return SimpleResponseEntity.failed("The project name has been used")
        } else {
            return SimpleResponseEntity.success(
                "Add project success",
                projectService.addProject(project, user.userId, chain ?: "")
            )
        }
    }

    @GetMapping("/project/check")
    fun checkProject(@RequestParam projectId: Long, @RequestParam checkStatus: Int): Any {
        val updateCnt = projectService.checkProject(projectId, checkStatus)
        return mapOf("code" to 200, "message" to "check success", "updateCnt" to updateCnt)
    }

    @GetMapping("/project/checkList")
    fun checkProject(): List<Project> {
        return projectService.getCheckList()
    }

    @PostMapping("/project/update")
    fun updateProject(@RequestBody project: Project): SimpleResponseEntity<Project> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        if (projectApplyService.getUserPrivilege(user.userId).not() && project.creatorId != user.userId) {
            return SimpleResponseEntity.failed("You are not allowed to create a project")
        }

        val updateProject = projectService.updateProject(project)
        if (updateProject == null) {
            return SimpleResponseEntity.failed("The project URL or name has been used")
        } else {
            return SimpleResponseEntity.success("Successfully Saved !", updateProject)
        }
    }

    @GetMapping("/project/{projectId}")
    fun getProject(@PathVariable("projectId") projectId: Long): Project? {
        return projectService.getProject(projectId)
    }

    @GetMapping("/project/byUrl/{url}")
    fun getProjectByUrl(@PathVariable("url") projectUrl: String): Project? {
        val project = projectService.getProjectByUrl(projectUrl) ?: return null
        val ut = userTwitterService.getUserInfo(project.creatorId)
        if (ut != null && ut.connected) {
            project.twitterLink = "https://twitter.com/" + ut.twitterName
        }
        val tg = telegramService.getUserInfo(project.creatorId)
        if (tg != null && tg.connected) {
            project.telegramLink = "https://t.me/" + tg.username
        }
        return project
    }

    @GetMapping("/project/overviewByUrl/{url}")
    fun getProjectOverviewByUrl(@PathVariable("url") projectUrl: String): ResponseEntity<ProjectOverview> {
        val authentication = SecurityContextHolder.getContext().authentication
        val chain = (authentication as? com.rewardoor.app.auth.AddressAuthentication)?.chain
        val project = projectService.getProjectByUrl(projectUrl) ?: return ResponseEntity.notFound().build()
        if (project.chain.lowercase() != chain?.lowercase()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build()
        }
        return ResponseEntity.ok(getProjectPreview(project))
    }

    @GetMapping("/project/{projectId}/overview")
    fun projectOverview(@PathVariable("projectId") projectId: Long): ResponseEntity<ProjectOverview> {
        val project = projectService.getProject(projectId) ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(getProjectPreview(project))
    }

    private fun getProjectPreview(project: Project): ProjectOverview {
        val campaigns = campaignService.getCampaignByProjectId(project.projectId)
        val credentials = credentialService.getCredentialByProjectId(project.projectId)
        val campaignStats = campaigns.map {
            val stats = campaignService.getCampaignByStats(it.campaignId)
            val size = stats.attendees.size.toLong()
            CampaignStats(
                it,
                it.nft != null,
                it.credentialId != null,
                it.points != null,
                if (size <= 0) 0 else Random.nextLong(0, size),
                if (size <= 0) 0 else Random.nextLong(0, size),
                if (size <= 0) 0 else Random.nextLong(0, size)
            )
        }
        val overview = ProjectOverview(
            project,
            actionCount = Random.nextLong(10, 1000),
            credentialCount = credentials.size.toLong(),
            nftCount = Random.nextLong(0, 1),
            campaignCount = campaigns.size.toLong(),
            campaignStats
        )
        return overview
    }

    @PostMapping("/project/{projectId}/genToken")
    fun generateAPIToken(@PathVariable("projectId") projectId: Long): String {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        return projectService.generateAPIToken(projectId, user.userId)
    }

    @GetMapping("/project/stats/{id}/points")
    fun getCampaignPointsStats(@PathVariable("id") id: Long): PointStats {
        val points = resultStatsService.getProjectPoints(id).sortedByDescending { it.points }
        return PointStats(points.sumOf { it.points }, points)
    }


//    @GetMapping("/project/home")
//    fun getProjectsAndCampaigns(): Map<String, Any> {
//        val doGet = redisTemplate.opsForValue().get("get_home_projects")
//        val projects: List<Project>
//        val campaigns: List<HomeCampaignSetting>
//        if (doGet == null || doGet == "true") {
//            projects = projectService.getHomeProjects()
//            campaigns = projectService.getHomeCampaigns()
//        } else {
//            projects = emptyList()
//            campaigns = emptyList()
//        }
//        return mapOf(
//            "code" to 200,
//            "projects" to projects,
//            "campaigns" to campaigns
//        )
//    }

    @GetMapping("/project/coreData")
    fun getCoreData(): Map<String, Any> {
        val evmUserCnt = userService.getEvmUserCnt()
        val tonUserCnt = userService.getTonUserCnt()
        val twitterUserCnt = userTwitterService.getTwitterUserCnt()
        val dcUserCnt = dcService.getDcUserCnt()
        val tgUserCnt = telegramService.getTgUserCnt()
        val credentialCnt = credentialService.getCredentialCntFromRedis()
        val pointsClaimedCnt = campaignService.getPointsClaimedCntFromRedis()
        val totalCnt = evmUserCnt + tonUserCnt + twitterUserCnt + dcUserCnt + tgUserCnt
        val rewardsClaimedCnt = participantRepo.getRewardsClaimedCnt()
        val onChainUserCnt = evmUserCnt + tonUserCnt
        return mapOf(
            "code" to 200,
            "totalCnt" to totalCnt,
            "credentialCnt" to credentialCnt,
            "rewardsClaimedCnt" to rewardsClaimedCnt,
            "pointsClaimedCnt" to pointsClaimedCnt,
            "onChainUserCnt" to onChainUserCnt
        )
    }

    @PostMapping("/project/setHome")
    fun setProjectHome(
        @RequestBody homeSettings: HomeProjectsCampaignsSBTsSetting
    ): Map<String, Any> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        println("${user.wallet} is setting project home")
        val adminAddressA = "******************************************" // Keyla
        val adminAddressB = "******************************************" // LakeHu
        if (user.wallet.lowercase(Locale.getDefault()) != adminAddressA.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressB.lowercase(Locale.getDefault())
        ) {
            return mapOf(
                "code" to 200,
                "message" to "你没有权限设置"
            )
        }
        val campaigns = homeSettings.campaigns
        for (campaign in campaigns) {
            val isCampaignValid = campaignService.getCampaignById(campaign.campaignId) != null
            if (!isCampaignValid) {
                return mapOf(
                    "code" to 400,
                    "message" to "The campaign ID does not exist."
                )
            }
        }
        for (sbt in homeSettings.sbts) {
            val isSbtValid = sbtRewardRepo.getSBTById(sbt.sbtId) != null
            if (!isSbtValid) {
                return mapOf(
                    "code" to 400,
                    "message" to "The SBT ID does not exist."
                )
            }
        }
        for (project in homeSettings.projects) {
            val isProjectValid = projectService.getProject(project.projectId) != null
            if (!isProjectValid) {
                return mapOf(
                    "code" to 400,
                    "message" to "The Project ID does not exist."
                )
            }
        }
        projectService.setHomeProjectsAndCampaignsAndSBTs(homeSettings)
        return mapOf(
            "code" to 200,
            "homeSettings" to projectService.getHomeProjectsAndCampaignsAndSBTs()
        )
    }

    @GetMapping("/project/home")
    fun getProjectsAndCampaigns(): Map<String, Any> {
        val homeSettings = projectService.getHomeProjectsAndCampaignsAndSBTs()
        return mapOf(
            "code" to 200,
            "homeSettings" to homeSettings
        )
    }

    @PostMapping("/project/setHome/template")
    fun setProjectHomeTemplate(
        @RequestBody templateSettings: HomeProjectsTemplateSettings
    ): Map<String, Any> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        println("${user.wallet} is setting project home s7")
        val adminAddressA = "******************************************" // Keyla
        val adminAddressB = "******************************************" // LakeHu
        if (user.wallet.lowercase(Locale.getDefault()) != adminAddressA.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressB.lowercase(Locale.getDefault())
        ) {
            return mapOf(
                "code" to 200,
                "message" to "你没有权限设置"
            )
        }
        for (campaignId in templateSettings.campaignIds) {
            val isCampaignValid = campaignService.getCampaignById(campaignId) != null
            if (!isCampaignValid) {
                return mapOf(
                    "code" to 400,
                    "message" to "The campaign ID does not exist."
                )
            }
        }
        return mapOf(
            "code" to 200,
            "template" to projectService.setHomeProjectsTemplates(templateSettings)
        )
    }

    @GetMapping("/project/home/<USER>")
    fun getProjectTemplates(@RequestParam name: String): Map<String, Any?> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val templateSettings = projectService.getHomeProjectsTemplates(name, user.userId)
        return mapOf(
            "code" to 200,
            "templateSettings" to templateSettings
        )
    }

    @PostMapping("/project/{projectId}/key")
    fun generateKey(@PathVariable("projectId") projectId: Long): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
//        checkIsCreator(principal, projectId, user)
        val project = projectService.getProject(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        checkIsAdmin(user.userId, project, checkAddress)
//        if (user.userId != creatorId) throw ResponseStatusException(
//            HttpStatus.FORBIDDEN,
//            "You are not the creator of this project"
//        )
        projectService.generateKey(projectId)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    @PostMapping("/project/{projectId}/callback")
    fun updateProjectCallbackUrl(
        @PathVariable("projectId") projectId: Long,
        @RequestBody callbackDto: SetCallbackDto
    ): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val user = userService.getUserByPrincipal(principal)!!
//        if (user.userId != creatorId) throw ResponseStatusException(
//            HttpStatus.FORBIDDEN,
//            "You are not the creator of this project"
//        )
        val project = projectService.getProject(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        checkIsAdmin(user.userId, project, checkAddress)
        try {
            val uri = URI.create(callbackDto.callbackUrl)
            if (!uri.scheme.startsWith("http")) throw ResponseStatusException(
                HttpStatus.BAD_REQUEST,
                "Invalid callback url"
            )
        } catch (e: Exception) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid callback url")
        }
        projectService.updateProjectCallbackUrl(projectId, callbackDto.callbackUrl)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    @GetMapping("/project/{projectId}/externalConfig")
    fun getProjectExternalConfig(@PathVariable("projectId") projectId: Long): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val user = userService.getUserByPrincipal(principal)!!
//        checkIsCreator(principal, projectId, user)
        val project = projectService.getProject(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        checkIsAdmin(user.userId, project, checkAddress)
//        if (user.userId != creatorId) throw ResponseStatusException(
//            HttpStatus.FORBIDDEN,
//            "You are not the creator of this project"
//        )
        return projectService.getProjectExternalConfig(projectId)
            ?.let { ProjectExternalConfigDto.fromModel(it) }
            ?: ProjectExternalConfigDto(projectId.toString())
    }

    @PostMapping("/project/{projectId}/callback/status")
    fun updateProjectCallbackStatus(
        @PathVariable("projectId") projectId: Long,
        @RequestBody callbackDto: CallbackDto
    ): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val user = userService.getUserByPrincipal(principal)!!
        if (user.userId != creatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "You are not the creator of this project"
        )

        projectService.updateProjectCallbackStatus(projectId, callbackDto.enable)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    @PostMapping("/project/{projectId}/callback/update")
    fun updateProjectCallback(
        @PathVariable("projectId") projectId: Long,
        @RequestBody callbackDto: CallbackDto
    ): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val user = userService.getUserByPrincipal(principal)!!
        if (user.userId != creatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "You are not the creator of this project"
        )

        projectService.updateProjectCallback(projectId, callbackDto.enable, callbackDto.callbackUrl)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    /*@GetMapping("testCallback")
     fun testCallback() {
         val config = projectService.getProjectExternalConfig(217262530358)!!
         val user = userService.getUserByAddress("0x3beff95bbb844015372075aae6fe8ff1e0de5d27")!!
         callbackService.callback(user, 217262530358, 1, System.currentTimeMillis())
     }*/

    @GetMapping("/project/admins/{id}")
    fun getProjectAdmins(@PathVariable("id") projectId: Long): List<Admin>? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val project = projectService.getProject(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )

//        if (user.userId != project.creatorId) throw ResponseStatusException(
//            HttpStatus.FORBIDDEN,
//            "You are not the creator of this project"
//        )
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        checkIsAdmin(user.userId, project, checkAddress)

        val admins = adminService.getAdminList(projectId)
        if (project.chain == "ton") {
            admins.forEach {
                try {
                    it.wallet = TonAddressUtils.hexToUserFriendly(it.wallet)
                } catch (e: Exception) {
                }
            }
        }
        return admins
    }

    @PostMapping("/project/admin/nonce")
    fun adminOpNonce(@RequestBody request: AdminNonceRequest): SimpleResponseEntity<String> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
//        if (!checkIsCreator(
//                principal,
//                request.projectId,
//                user
//            )
//        ) return SimpleResponseEntity.failed("You are not the creator of this project")
        val project = projectService.getProject(request.projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val chain = project.chain
        val checkAddress = when (chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        checkIsAdmin(user.userId, project, checkAddress)
        val nonce = adminService.createAdminNonce(user.userId, request.projectId, request.address, request.op)
        return SimpleResponseEntity.success("SUCCESS", nonce)
    }

    @PostMapping("/project/addAdmin")
    fun addAdmin(@RequestBody admin: Admin): SimpleResponseEntity<Admin> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain?.lowercase() ?: ""
        val userWallet = when (chain) {
            "sui" -> user.suiAddress
            "eth" -> user.wallet
            "ton" -> user.ton.tonWallet ?: ""
            else -> ""
        }
        if (!checkIsCreator(principal, admin.projectId, user)) {
            return SimpleResponseEntity.failed("You are not the creator of this project")
        } else if (adminService.isAdminExist(admin)) {
            return SimpleResponseEntity.failed("The administrator has already been added")
        } else if (adminService.isOtherProjectAdmin(admin)) {
            return SimpleResponseEntity.failed("The address is already linked to another project. Please try another address")
        } else if (!adminService.verifySign(
                principal.toLong(),
                admin.projectId,
                userWallet,
                admin.wallet,
                admin.op,
                admin.sign,
                chain
            )
        ) {
            return SimpleResponseEntity.failed("Invalid signature")
        } else {
            return SimpleResponseEntity.success("Add administrator success", adminService.addAdmin(admin))
        }
    }

    @PostMapping("/project/addTonAdmin")
    fun addTonAdmin(@RequestBody adminReq: TonAdminRequest): SimpleResponseEntity<Admin> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain?.lowercase() ?: ""
        if (chain != "ton") {
            return SimpleResponseEntity.failed("This endpoint is only for TON chain")
        }

        val admin = Admin(wallet = TonAddressUtils.toHexAddress(adminReq.address), projectId = adminReq.projectId)
        if (!checkIsCreator(principal, admin.projectId, user)) {
            return SimpleResponseEntity.failed("You are not the creator of this project")
        } else if (adminService.isAdminExist(admin)) {
            return SimpleResponseEntity.failed("The administrator has already been added")
        } else if (adminService.isOtherProjectAdminSimple(admin)) {
            return SimpleResponseEntity.failed("The address is already linked to another project. Please try another address")
        } else {
            return SimpleResponseEntity.success("Add administrator success", adminService.addAdmin(admin))
        }
    }

    @PostMapping("/project/deleteAdmin")
    fun deleteAdmin(@RequestBody admin: Admin): SimpleResponseEntity<Admin?> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain?.lowercase() ?: ""
        val userWallet = when (chain) {
            "sui" -> user.suiAddress
            "eth" -> user.wallet
            "ton" -> user.ton.tonWallet ?: ""
            else -> ""
        }
        if (!checkIsCreator(principal, admin.projectId, user)) {
            return SimpleResponseEntity.failed("You are not the creator of this project")
        } else if (!adminService.verifySign(
                principal.toLong(),
                admin.projectId,
                userWallet,
                admin.wallet,
                admin.op,
                admin.sign,
                chain
            )
        ) {
            return SimpleResponseEntity.failed("Invalid signature")
        } else {
            return SimpleResponseEntity.success("SUCCESS", adminService.deleteAdmin(admin))
        }
    }

    @PostMapping("/project/deleteTonAdmin")
    fun deleteTonAdmin(@RequestBody adminReq: TonAdminRequest): SimpleResponseEntity<Admin?> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain?.lowercase() ?: ""
        if (chain != "ton") {
            return SimpleResponseEntity.failed("This endpoint is only for TON chain")
        }

        val admin = Admin(wallet = TonAddressUtils.toHexAddress(adminReq.address), projectId = adminReq.projectId)
        if (!checkIsCreator(principal, admin.projectId, user)) {
            return SimpleResponseEntity.failed("You are not the creator of this project")
        } else {
            return SimpleResponseEntity.success("SUCCESS", adminService.deleteAdmin(admin))
        }
    }

    fun checkIsCreator(principal: String, projectId: Long, user: User): Boolean {
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        if (user.userId != creatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "You are not the creator of this project"
        ) else {
            return true
        }
    }

    fun checkIsAdmin(userId: Long, project: Project, address: String) {
        if (userId == project.creatorId) return
        val adminList = adminService.getAdminList(project.projectId).orEmpty()
        val admins = adminList.map { it.userId }.orEmpty()
        val adminAddresses = adminList.map { it.wallet }
        println("address is $address and adminAddresses is $adminAddresses")
        if (!admins.contains(userId) && !adminAddresses.contains(address)) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "User is not admin of this project")
        }
    }

}

class TonAdminRequest(
    val projectId: Long,
    val address: String
)