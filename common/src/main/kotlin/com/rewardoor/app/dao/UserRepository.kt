package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.*
import com.rewardoor.model.*
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class UserRepository {
    fun addUserWithWallet(user: User, wallet: String, newUserId: Long): Long {
        val curUser =
            TBUserEvm.select { TBUserEvm.evmAddress eq wallet }.limit(1).map { it[TBUserEvm.userId] }.firstOrNull()
        if (curUser != null) {
            return findUserById(curUser)!!.userId
        }
        TBUser.insert {
            it[userId] = newUserId
            it[avatar] = user.avatar
            it[email] = user.email
            it[name] = user.name
            it[newUser] = 1
        }
        bindEvmWallet(newUserId, wallet)
        return newUserId
    }

    fun addUser(user: User, newUserId: Long): Long {
        TBUser.insert {
            it[userId] = newUserId
            it[avatar] = user.avatar
            it[email] = user.email
            it[name] = user.name
            it[newUser] = 1
        }
        return newUserId
    }

    fun updateNewUserStatus(userId: Long, newUser: Boolean) {
        TBUser.update({ TBUser.userId eq userId }) {
            it[TBUser.newUser] = if (newUser) 1 else 0
        }
    }

    fun markMergeDeletedUser(userId: Long) {
        TBUser.update({ TBUser.userId eq userId }) {
            it[TBUser.newUser] = -1
        }
    }

    fun findUserByWallet(wallet: String): User? {
        val evm =
            TBUserEvm.select { TBUserEvm.evmAddress eq wallet }.limit(1).map(::mapUserEvm).firstOrNull() ?: return null
        val user = TBUser.select { TBUser.userId eq evm.userId }.limit(1).map(::mapUser).first()
        val zk =
            TBZKLogin.select { TBZKLogin.userId eq evm.userId }.limit(1).map(ZKLoginRepository::mapper).firstOrNull()
        user.evm = evm
        if (zk != null) user.zk = zk
        return user
    }

    fun updateEvmUserId(oldUserId: Long, newUserId: Long) {
        TBUserEvm.update({ TBUserEvm.userId eq oldUserId }) {
            it[TBUserTwitter.userId] = newUserId
        }
    }

    fun updateSuiUserId(oldUserId: Long, newUserId: Long) {
        TBUserSui.update({ TBUserSui.userId eq oldUserId }) {
            it[TBUserSui.userId] = newUserId
        }
    }

    fun findUserById(uid: Long): User? {
        val user = TBUser.select { TBUser.userId eq uid }.limit(1).map(::mapUser).firstOrNull() ?: return null
        val evm = TBUserEvm.select { TBUserEvm.userId eq uid }.limit(1).map { it[TBUserEvm.evmAddress] }.firstOrNull()
        val sui = TBUserSui.select { TBUserSui.userId eq uid }.limit(1).map { it[TBUserSui.address] }.firstOrNull()
        val twitterName = TBUserTwitter.select { TBUserTwitter.userId eq uid }.limit(1).map(::mapUserTwitter)
            .firstOrNull()?.twitterName
        if (!twitterName.isNullOrEmpty()) {
            user.twitterName = twitterName
        }
        println("twitter name is " + twitterName + "  " + user.wallet)
        val dcName = TBUserDiscord.select { TBUserDiscord.userId eq uid }.limit(1).map { it[TBUserDiscord.username] }
            .firstOrNull()
        val tgName = TBUserTelegram.select { TBUserTelegram.userId eq uid }.limit(1).map { it[TBUserTelegram.username] }
            .firstOrNull()
        if (dcName != null) {
            user.dcName = dcName
        }
        if (tgName != null) {
            user.tgName = tgName
        }
        val zk = TBZKLogin.select { TBZKLogin.userId eq uid }.limit(1).map(ZKLoginRepository::mapper).firstOrNull()
        user.evm = UserEvm(uid, evm)
        user.sui = UserSui(uid, sui, "")
        val tonRecord =
            TBUserTon.select { TBUserTon.userId eq uid }.limit(1).firstOrNull()
        val tonAddress = tonRecord?.get(TBUserTon.address)
        val tonPublicKey = tonRecord?.get(TBUserTon.publicKey)
        val tonHexAddress = tonRecord?.get(TBUserTon.hexAddress)
        user.ton = UserTon(uid, tonAddress, tonPublicKey, tonHexAddress)
        if (zk != null) user.zk = zk
        if (user.wallet == "" && twitterName != null) { //未用钱包登录，用twitter登录
            user.isTwitterLogin = true
            println("new user twitter name is " + user.twitterName + "  " + twitterName)
        }
        val hasWiseScore = TBUserWiseScore
            .select { TBUserWiseScore.userId eq user.userId }
            .limit(1)
            .firstOrNull() != null
        return user.apply {
            this.hasWiseScore = hasWiseScore
        }
    }

    fun findTgIdByUserId(uid: Long): Long? {
        val tgId = TBUserTelegram.select { TBUserTelegram.userId eq uid }.limit(1).map { it[TBUserTelegram.tgId] }
            .firstOrNull()
        return tgId
    }

    fun findUsersByIds(uidList: List<Long>): List<User> {
        if (uidList.isEmpty()) return emptyList()
        val users = TBUser
            .select { TBUser.userId inList uidList }
            .map(::mapUser)
            .associateBy { it.userId } // 将结果转为 Map<userId, User>
        val evmAddresses = TBUserEvm
            .select { TBUserEvm.userId inList uidList }
            .associate { it[TBUserEvm.userId] to it[TBUserEvm.evmAddress] }
        val twitterNames = TBUserTwitter
            .select { TBUserTwitter.userId inList uidList }
            .associate { it[TBUserTwitter.userId] to it[TBUserTwitter.twitterName] }
        val discordNames = TBUserDiscord
            .select { TBUserDiscord.userId inList uidList }
            .associate { it[TBUserDiscord.userId] to it[TBUserDiscord.username] }
        val telegramNames = TBUserTelegram
            .select { TBUserTelegram.userId inList uidList }
            .associate { it[TBUserTelegram.userId] to it[TBUserTelegram.username] }
        val zkLogins = TBZKLogin
            .select { TBZKLogin.userId inList uidList }
            .associate { it[TBZKLogin.userId] to ZKLoginRepository.mapper(it) }
        val tonAddresses = TBUserTon
            .select { TBUserTon.userId inList uidList }
            .groupBy { it[TBUserTon.userId] }
            .mapValues { entry ->
                val tonAddress = entry.value.firstOrNull()?.get(TBUserTon.address)
                val tonPublicKey = entry.value.firstOrNull()?.get(TBUserTon.publicKey)
                val tonHexAddress = entry.value.firstOrNull()?.get(TBUserTon.hexAddress)
                UserTon(entry.key, tonAddress, tonPublicKey, tonHexAddress)
            }
        val wiseScores = TBUserWiseScore
            .select { TBUserWiseScore.userId inList uidList }
            .map { it[TBUserWiseScore.userId] }
            .toSet()
        return users.values.map { user ->
            val userId = user.userId
            user.evm = UserEvm(userId, evmAddresses[userId])
            user.twitterName = twitterNames[userId].orEmpty()
            user.dcName = discordNames[userId].orEmpty()
            user.tgName = telegramNames[userId].orEmpty()
            user.ton = tonAddresses[userId] ?: UserTon(userId, null, null)
            user.zk = zkLogins[userId] ?: UserZK(userId, null, null, null, null, null)
            user.hasWiseScore = wiseScores.contains(userId)
            if (user.wallet.isEmpty() && user.twitterName.isNotEmpty()) {
                user.isTwitterLogin = true
            }
            user
        }
    }


    fun getUsersByUserIdList(userIdList: List<Long>): List<User>? {
        return TBUser
            .select { TBUser.userId inList userIdList }
            .map(::mapUser)
    }

    fun bindEvmWallet(userId: Long, wallet: String): Int {
        return TBUserEvm.insertIgnore {
            it[TBUserEvm.userId] = userId
            it[evmAddress] = wallet
        }.insertedCount
    }

    fun findEvmWallet(wallet: String): UserEvm? {
        return TBUserEvm.select { TBUserEvm.evmAddress eq wallet }.limit(1).map(::mapUserEvm).firstOrNull()
    }

    fun getEvmUserWalletById(userId: Long): UserEvm? {
        return TBUserEvm.select { TBUserEvm.userId eq userId }.limit(1).map(::mapUserEvm).firstOrNull()
    }

    fun getEvmUsersByUserIdList(userIdList: List<Long>): List<UserEvm>? {
        return TBUserEvm
            .select { TBUserEvm.userId inList userIdList }
            .map(::mapUserEvm)
    }

    fun mapUser(r: ResultRow): User {
        return User(
            r[TBUser.userId],
            r[TBUser.avatar],
            r[TBUser.email].orEmpty(),
            r[TBUser.name].orEmpty(),
            r[TBUser.newUser] == 1
        )
    }

    fun mapUserTwitter(r: ResultRow): UserTwitterInfo {
        return UserTwitterInfo(
            r[TBUserTwitter.userId],
            r[TBUserTwitter.state],
            r[TBUserTwitter.codeChallenge],
            r[TBUserTwitter.accessToken],
            r[TBUserTwitter.refreshToken],
            r[TBUserTwitter.twitterId],
            r[TBUserTwitter.twitterName],
            r[TBUserTwitter.twitterScreenName],
            r[TBUserTwitter.twitterProfileImage],
            r[TBUserTwitter.connected] == 1,
            SimpleStatus.fromValue(r[TBUserTwitter.status]),
        )
    }

    fun mapUserEvm(r: ResultRow): UserEvm {
        return UserEvm(
            r[TBUserEvm.userId],
            r[TBUserEvm.evmAddress]
        )
    }

    fun findUsersById(userIds: List<Long>): List<User> {
        return TBUser.select { TBUser.userId inList userIds }.map(::mapUser)
    }

    fun getEvmAddressCount(): Long {
        return TBUserEvm.slice(TBUserEvm.evmAddress.count()).selectAll()
            .single()[TBUserEvm.evmAddress.count()]
    }

}
