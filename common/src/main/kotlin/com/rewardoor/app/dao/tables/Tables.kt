package com.rewardoor.app.dao.tables

import com.rewardoor.app.dao.tables.TBAirDrop.nullable
import com.rewardoor.app.dao.tables.TBTgUserPush.default
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.datetime
import org.jetbrains.exposed.sql.javatime.timestamp

object TBSequence : LongIdTable("tb_sequence", "id") {
    val placeHolder = varchar("place_holder", 10).uniqueIndex()
}

object TBUser : LongIdTable("tb_user", "id") {
    val userId = long("user_id").uniqueIndex()
    val name = varchar("name", 128).nullable()
    val wallet = varchar("wallet_address", 128)
    val arAddress = varchar("ar_address", 128)
    val avatar = varchar("avatar", 256)
    val newUser = integer("new_user")
    val email = varchar("email", 128).nullable().uniqueIndex()
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBAdmin : LongIdTable("tb_project_admin", "id") {
    val userId = long("user_id")
    val wallet = varchar("wallet_address", 128)
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val isOwner = integer("is_owner")
    val status = integer("status")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBUserEvm : LongIdTable("tb_user_evm", "id") {
    val userId = long("user_id").uniqueIndex()
    val evmAddress = varchar("address", 128)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBUserAr : LongIdTable("tb_user_ar", "id") {
    val userId = long("user_id").uniqueIndex()
    val arAddress = varchar("ar_address", 128)
    val publicKey = varchar("public_key", 128)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBTwitterStateChallenge : LongIdTable("tb_tw_state_challenge", "id") {
    val state = varchar("state", 128)
    val challenge = varchar("challenge", 128)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBCompany : LongIdTable("tb_company", "id") {
    val companyId = long("company_id")
    val companyName = varchar("company_name", 128)
    val creatorId = long("creator_id")
    val companyDescription = text("company_description").nullable()
    val logo = varchar("logo", 128)
    val channelName = varchar("channel_name", 128)
    val channelLink = varchar("channel_link", 128)
    val twitterLink = varchar("twitter_link", 128)
    val webUrl = varchar("web_url", 128)
    val aboutBgImage = varchar("about_bg_image", 128)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBLayerOne : LongIdTable("tb_layer_one", "id") {
    val layerOneId = long("layer_one_id")
    val name = varchar("name", 128)
    val creatorId = long("creator_id")
    val icon = varchar("icon", 128)
    val url = varchar("url", 128)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBCompanyLayerOneRelation : LongIdTable("tb_company_layer_one_relation", "id") {
    val companyId = long("company_id")
    val layerOneId = long("layer_one_id")
    val status = integer("status")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}


object TBCompanyConfig : LongIdTable("tb_company_config", "id") {
    val companyId = long("company_id")
    val configKey = varchar("config_key", 255)
    val configValue = varchar("config_value", 255)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBProject : LongIdTable("tb_project", "id") {
    val projectId = long("project_id")
    val companyId = long("company_id")
    val projectName = varchar("project_name", 128)
    val projectUrl = varchar("project_url", 255)
    val avatarUrl = varchar("avatar_url", 255)
    val creatorId = long("creator_id")
    val tags = text("tags")
    val theme = integer("theme")
    val projectDescription = text("project_description").nullable()
    val websiteUrl = varchar("website_url", 255)
    val telegramUrl = varchar("telegram_url", 255)
    val twitterLink = varchar("twitter_link", 255)
    val telegramLink = varchar("telegram_link", 255)
    val discordLink = varchar("discord_link", 255)
    val banner = varchar("banner", 255)
    val emptyCampaignText = varchar("empty_campaign_text", 255)
    val projectCustomItemA = varchar("project_custom_item_a", 255)
    val layerOneList = text("layer_one_list")
    val chain = varchar("chain", 64) // 新增字段，表示项目所属链
    val goal = varchar("goal", 128) // 项目目标
    val tgHandle = varchar("tg_handle", 128) // telegram handle，用来沟通项目方
    val checkStatus = integer("check_status") // project审核状态，0 - under Review , 1 - passed , 2 - rejected
    val projectEmail = varchar("project_email", 128) // project创建时联系的email
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBCredential : LongIdTable("tb_credential", "id") {
    val credentialId = long("credential_id")
    val name = varchar("name", 255)
    val nameExp = varchar("name_exp", 255)
    val labelType = integer("label_type")
    val link = varchar("link", 255)
    val picUrl = varchar("pic_url", 255)
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val groupType = integer("group_type")
    val spaceId = varchar("space_id", 255)
    val campaignId = long("campaign_id")
    val isVerified = integer("is_verified")
    val roleId = long("role_id")
    val roleName = varchar("role_name", 128)
    val visitPageName = varchar("page_name", 128)
    val proposalId = varchar("proposal_id", 255)
    val title = varchar("title", 255)
    val description = varchar("description", 255)
    val options = text("options")
    val extra_info = text("extra_info").nullable()
    val ctaApiLink = varchar("cta_api_link", 255)
    val customUserAddressType = integer("custom_user_address_type")
    val verifyApiLink = varchar("verify_api_link", 255)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBCredentialGroup : LongIdTable("tb_credential_group", "id") {
    val groupId = long("group_id")
    val name = varchar("name", 255)
    val groupType = integer("group_type")

    //    val credentialList = text("credential_list")
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val campaignId = long("campaign_id")
    val status = integer("status")
    val existActivityId = integer("exist_activity_id")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBPoint : LongIdTable("tb_point", "id") {
    val pointId = long("point_id")
    val number = long("number")
    val methodType = integer("method_type")
    val unlimited = varchar("unlimited", 255)
    val rewardNum = long("reward_num")
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val claimedNum = long("claimed_num")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBSBTReward : LongIdTable("tb_sbt_reward", "id") {
    val sbtId = long("sbt_id")
    val name = varchar("name", 255)
    val category = integer("category")
    val activityId = integer("activity_id")
    val activityUrl = varchar("activity_url", 255)
    val picUrl = varchar("pic_url", 255)
    val number = long("number")
    val methodType = integer("method_type")
    val unlimited = varchar("unlimited", 32)
    val rewardNum = long("reward_num")
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBSuiSbtReward : LongIdTable("tb_sui_sbt_reward", "id") {
    val sbtId = long("sbt_id")
    val activityId = long("activity_id")
    val name = varchar("name", 255)
    val desc = varchar("desc", 255)
    val url = varchar("url", 255)
    val contractAddress = varchar("contract_address", 255)
    val objectId = varchar("object_id", 255)
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBSuiSbtSync : LongIdTable("tb_sui_sbt_sync", "id") {
    val activityId = long("activity_id")
    val sbtId = long("sbt_id")
    val projectId = long("project_id")
    val name = varchar("name", 255)
    val desc = varchar("desc", 255)
    val url = varchar("url", 255)
    val contractAddress = varchar("contract_address", 255)
    val objectId = varchar("object_id", 255)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBNft : LongIdTable("tb_nft", "id") {
    val nftId = long("nft_id")
    val name = varchar("name", 255)
    val symbol = varchar("symbol", 255)
    val contract = varchar("contract", 255)
    val chainId = integer("chain_id")
    val coverUrl = varchar("cover_url", 255)
    val picUrl = varchar("pic_url", 255)
    val mintCap = long("mint_cap")
    val unlimited = varchar("unlimited", 255)
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val methodType = integer("method_type")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBToken : LongIdTable("tb_token", "id") {
    val tokenId = long("token_id")
    val name = varchar("name", 128)
    val netWork = varchar("net_work", 255)
    val number = long("number")
    val contract = varchar("contract", 255)
    val chainId = integer("chain_id")
    val methodType = integer("method_type")
    val rewardNum = long("reward_num")
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}


object TBNftGroup : LongIdTable("tb_nft_group", "id") {
    val nftId = long("nft_id")
    val name = varchar("name", 255)
    val symbol = varchar("symbol", 255)
    val contract = varchar("contract", 255)
    val chainId = integer("chain_id")
    val coverUrl = varchar("cover_url", 255)
    val picUrl = varchar("pic_url", 255)
    val mintCap = long("mint_cap")
    val unlimited = varchar("unlimited", 255)
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val groupId = long("group_id")
    val methodType = integer("method_type")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBParticipant : LongIdTable("tb_participant", "id") {
    val userId = long("user_id")
    val campaignId = long("campaign_id")
    val wallet = varchar("wallet_address", 128)

    //    val nfts = varchar("nfts", 256)
//    val points = varchar("points", 256)
    val pointNum = long("point_num")
    val isJoin = varchar("is_join", 128)
    val isVisit = varchar("is_visit", 128)
    val credentials = text("credentials")
    val verifiedCredentials = text("verified_credentials")
    val participantDate = datetime("participant_date")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBCampaign : LongIdTable("tb_campaign", "id") {
    val campaignId = long("campaign_id")
    val name = varchar("name", 255)
    val picUrl = varchar("pic_url", 255)
    val description = text("description")
    val shareText = text("share_text")
    val startAt = datetime("start_at")
    val endAt = datetime("end_at")
    val status = integer("status")
    val reward = text("reward")
    val rewardAction = varchar("reward_action", 255)
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val points = integer("points").nullable()
    val credential = long("credential_id").nullable()
    val nft = long("nft_id").nullable()
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBCampaignCredential : LongIdTable("tb_campaign_credential", "id") {
    val campaignCredentialId = long("campaign_credential_id")
    val campaignId = long("campaign_id")
    val credentialId = long("credential_id")
    val status = integer("status")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBProjectToken : LongIdTable("tb_project_token", "id") {
    val tokenId = long("token_id")
    val token = varchar("token", 256)
    val status = integer("status")
    val projectId = long("project_id")
    val creatorId = long("creator_id")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserCampaign : LongIdTable("tb_user_campaign", "id") {
    val userCampaignId = long("user_campaign_id")
    val projectId = long("project_id")
    val campaignId = long("campaign_id")
    val address = varchar("address", 255)
    val points = integer("points")
    val opToken = varchar("op_token", 255)
    val status = integer("status")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserCredential : LongIdTable("tb_user_credential", "id") {
    val userCredentialId = long("user_credential_id")
    val address = varchar("address", 255)
    val credentialId = long("credential_id")
    val campaignId = long("campaign_id")
    val opToken = varchar("op_token", 255)
    val status = integer("status")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserCredentialNew : LongIdTable("tb_user_credential_new", "id") {
    val userId = long("user_id")
    val address = varchar("address", 255)
    val credentialId = long("credential_id")
    val campaignId = long("campaign_id")
    val status = integer("status")
    val participantDate = datetime("participant_date")
    val isTwitterLogin = varchar("is_twitter_login", 64)
    val socialId = varchar("social_id", 128)
    val socialType = integer("social_type")
    val labelType = integer("label_type")
    val amount = long("amount")
    val amount2 = long("amount2")
    val amount3 = long("amount3")
    val amount4 = long("amount4")
    val totalAmount = long("total_amount")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserCampaignNew : LongIdTable("tb_user_campaign_new", "id") {
    val userId = long("user_id")
    val address = varchar("address", 255)
    val campaignId = long("campaign_id")
    val pointNum = long("point_num")
    val status = integer("status")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserGroupNew : LongIdTable("tb_user_group_new", "id") {
    val userId = long("user_id")
    val address = varchar("address", 255)
    val groupId = long("group_id")
    val pointNum = long("point_num")
    val status = integer("status")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserTwitter : LongIdTable("tb_user_twitter", "id") {
    val userId = long("user_id")
    val state = varchar("state", 255)
    val codeChallenge = varchar("code_challenge", 255)
    val accessToken = varchar("access_token", 255)
    val refreshToken = varchar("refresh_token", 255)
    val twitterId = varchar("twitter_id", 255)
    val twitterName = varchar("twitter_name", 255)
    val twitterScreenName = varchar("twitter_screen_name", 255)
    val twitterProfileImage = varchar("twitter_profile_image", 255)
    val connected = integer("connected")
    val status = integer("status")
}

object TBTwitterNameIdMapping : LongIdTable("tb_twitter_name_id_mapping", "id") {
    val twitterName = varchar("twitter_username", 255)
    val twitterId = varchar("twitter_id", 255)
}

object TBUserTelegram : LongIdTable("tb_user_telegram", "id") {
    val userId = long("user_id")
    val tgId = long("tg_id")
    val firstName = varchar("first_name", 255)
    val lastName = varchar("last_name", 255)
    val username = varchar("username", 255)
    val photoUrl = varchar("photo_url", 255)
    val authDate = datetime("auth_date")
    val connected = integer("connected")
}

object TBUserDiscord : LongIdTable("tb_user_discord", "id") {
    val userId = long("user_id")
    val dcId = varchar("dc_id", 255)
    val username = varchar("username", 255)
    val avatar = varchar("avatar", 255)
    val globalName = varchar("global_name", 255)
    val accessToken = varchar("access_token", 255)
    val refreshToken = varchar("refresh_token", 255)
    val tokenExpire = long("token_expire")
    val connected = integer("connected")
}

object TBUserDiscordVoiceAttendance : LongIdTable("tb_user_discord_voice_attendance", "id") {
    val userId = long("user_id")
    val dcUserId = varchar("dc_user_id", 255)
    val guildId = varchar("guild_id", 255)
    val channelId = varchar("channel_id", 255)
    val joinTime = timestamp("join_time")
    val leaveTime = timestamp("leave_time").nullable()
    val duration = long("duration")
}

object TBUserReward : LongIdTable("tb_user_reward", "id") {
    val rewardId = long("reward_id")
    val rewardType = integer("reward_type")
    val suiSbtObjectId = varchar("sbt_object_id", 255)
    val txHash = varchar("tx", 255)
    val userId = long("user_id")
    val groupId = long("group_id")
    val claimType = integer("claim_type")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBCredentialSign : LongIdTable("tb_credential_sign", "id") {
    val userId = long("user_id")
    val credentialId = long("credential_id")
    val rawData = varchar("raw_data", 255)
    val sign = varchar("sign", 255)
    val verified = integer("verified")
    val signTime = datetime("sign_time").nullable()
}

object TBCredentialAirdropAddress : LongIdTable("tb_credential_airdrop_address", "id") {
    val userId = long("user_id")
    val credentialId = long("credential_id")
    val address = varchar("wallet_address", 128)
    val verified = integer("verified")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserNFT : LongIdTable("tb_user_nft", "id") {
    val userId = long("user_id")
    val nftId = long("nft_id")
    val dummyId = varchar("dummy_id", 255)
    val signature = varchar("signature", 255)
    val tx = varchar("tx", 255)
}


object TBProjectExternalConfig : LongIdTable("tb_project_external_config", "id") {
    val projectId = long("project_id")
    val appKey = varchar("app_key", 255)
    val callbackUrl = varchar("callback_url", 255)
    val status = integer("status")
    val extra = text("extra")
}

object TBCallbackHistory : LongIdTable("tb_callback_history", "id") {
    val projectId = long("project_id")
    val credentialId = long("credential_id")
    val userId = long("user_id")
    val callbackUrl = varchar("callback_url", 255)
    val status = varchar("status", 128)
    val times = integer("times")
    val content = text("content")
}

object TBDummyId : LongIdTable("tb_dummy_id", "id") {
    val userId = long("user_id")
    val groupId = long("credential_group_id")
}

object TBAddressNonce : LongIdTable("tb_addr_nonce", "id") {
    val address = varchar("address", 255)
    val nonce = varchar("nonce", 255)
    val status = integer("status")
}

object TBZKLogin : LongIdTable("tb_zk_login", "id") {
    val userId = long("user_id")
    val issuer = varchar("issuer", 255)
    val sub = varchar("sub", 255)
    val identity = varchar("identity", 255)
    val salt = varchar("salt", 255)
    val address = varchar("address", 255)
}

object TBAdminNonceSign : LongIdTable("tb_admin_nonce_sign", "id") {
    val userId = long("user_id")
    val projectId = long("project_id")
    val address = varchar("address", 255)
    val nonce = varchar("nonce", 255)
    val op = varchar("op", 16)
    val sign = varchar("sign", 255)
    val status = integer("status")
}

object TBUnbind : LongIdTable("tb_unbind", "id") {
    val userId = long("user_id")
    val address = varchar("address", 255)
    val socialType = integer("social_type")
    val socialId = varchar("social_id", 128)
    val credentialId = long("credential_id")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBUserTon : LongIdTable("tb_user_ton", "id") {
    val userId = long("user_id").uniqueIndex()
    val address = varchar("address", 128)
    val hexAddress = varchar("hex_address", 128).uniqueIndex()
    val publicKey = varchar("public_key", 256)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBUserWiseScore : LongIdTable("tb_user_wise_score", "id") {
    val userId = long("user_id").uniqueIndex()
    val address = varchar("address", 128)
    val addressType = integer("address_type")
    val avatar = varchar("avatar", 256)
    val totalScore = integer("total_score")
    val wealthScore = integer("wealth_score")
    val identityScore = integer("identity_score")
    val socialScore = integer("social_score")
    val engagementScore = integer("engagement_score")
    val isNotCoinHolder = integer("is_not_coin_holder")
    val hasNotCoinTransaction = integer("has_not_coin_transaction")
    val hasTonStake = integer("has_ton_stake")
    val hasTonLiquidityProvide = integer("has_ton_liquidity_provide")
    val rank = integer("rank")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}


object TBUserWiseScoreTmp : LongIdTable("tb_user_wise_score_tmp", "id") {
    val userId = long("user_id").uniqueIndex()
    val address = varchar("address", 128)
    val addressType = integer("address_type")
    val avatar = varchar("avatar", 256)
    val totalScore = integer("total_score")
    val wealthScore = integer("wealth_score")
    val identityScore = integer("identity_score")
    val socialScore = integer("social_score")
    val engagementScore = integer("engagement_score")
    val rank = integer("rank")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBUserSBTList : LongIdTable("tb_user_sbt_list", "id") {
    val userId = long("user_id").uniqueIndex()
    val address = varchar("address", 128)
    val addressType = integer("address_type")
    val avatar = varchar("avatar", 256)
    val activityId = integer("activity_id")
    val sbtLink = varchar("sbt_link", 256)
    val claimedType = integer("claimed_type")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBLuckyDrawResult : LongIdTable("tb_user_lucky_draw", "id") {
    val userId = long("user_id").uniqueIndex()
    val fissionLevel = integer("fission_level")
    val tPointsNum = integer("t_points")
    val isEligibleToGenerateWiseScore = integer("is_wise_score")
    val isEligibleToGenerateSBT = integer("is_sbt")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBTPointsConsume : LongIdTable("tb_t_points_consume", "id") {
    val userId = long("user_id")
    val consumeType = integer("consume_type")
    val level = integer("level")
    val tPointsNum = integer("t_points")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBTPointsVanguard : LongIdTable("tb_t_points_vanguard", "id") {
    val userId = long("user_id")
    val level = integer("level")
    val tPointsNum = integer("t_points")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBUserShareLink : LongIdTable("tb_user_share_link", "id") {
    val userId = long("user_id").uniqueIndex()
    val socialType = integer("social_type")
    val shareLink = varchar("share_link", 128)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBTokenAirdrop : LongIdTable("tb_token_air_drop", "id") {
    val tokenName = varchar("token_name", 128)
    val address = varchar("address", 128)
    val userId = long("user_id")
    val amount = varchar("amount", 128)
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBProjectApply : LongIdTable("tb_project_apply", "id") {
    val userId = long("user_id")
    val projectName = varchar("project_name", 128)
    val category = varchar("category", 128)
    val tmaLink = varchar("tma_link", 128)
    val email = varchar("email", 128)
    val applyReason = text("apply_reason")
    val estimatedParticipants = integer("estimated_participants")
    val socialPlatforms = varchar("social_platforms", 128)
    val additionalSocialInfo = text("additional_social_info")
}

object TBUserProjectPrivilege : LongIdTable("tb_user_project_privilege", "id") {
    val userId = long("user_id")
    val status = integer("status")
}

object TBTelegramInvitation : LongIdTable("tb_telegram_invitation", "id") {
    val userId = long("user_id")
    val inviterTgId = long("inviter_tg_id")
    val inviteeTgId = long("invitee_tg_id")
    val inviteeFirstName = varchar("invitee_first_name", 128)
    val inviteeLastName = varchar("invitee_last_name", 128)
    val inviteeUsername = varchar("invitee_username", 128)
    val premium = integer("is_premium")
    val inviteAt = datetime("invite_at")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBTgLuckyDrawTimes : LongIdTable("tb_tg_lucky_draw_times", "id") {
    val userId = long("user_id")
    val tgUserId = long("tg_user_id")
    val times = integer("times")
    val lastDailyAddAt = datetime("last_daily_add_at")
    val lastAddAt = date("last_add_at")
    val createTime = datetime("created_at")
    val updateTime = datetime("updated_at")
}

object TBDailyLuckyDrawTimes : LongIdTable("tb_daily_lucky_draw_times", "id") {
    val userId = long("user_id")
    val times = integer("times")
    val usedTimes = integer("used_times")
    val date = varchar("date", 64)
    val nextAddAt = timestamp("next_add_at")
    val lastUseAt = timestamp("last_use_at")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBTgPrivateGroupInfo : LongIdTable("tb_tg_private_group_info", "id") {
    val tgId = long("tg_id").uniqueIndex()
    val privateHash = varchar("private_hash", 128).uniqueIndex()
    val status = varchar("status", 128)
    val operationAt = long("operation_at")
}

object TBWiseScoreOneTimeTask : LongIdTable("tb_wise_score_one_time_task", "id") {
    val userId = long("user_id")
    val taskName = varchar("task_name", 128)
    val taskTime = timestamp("task_time")
}

object TBWiseScoreInvite : LongIdTable("tb_wise_score_invite", "id") {
    val userId = long("user_id")
    val eventStage = integer("event_stage")
    val inviteCode = varchar("invite_code", 32)
    val totalTimes = integer("total_times").default(3)
    val usedTimes = integer("used_times").default(0)
    val status = integer("status").default(1)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBWiseInviteRecord : LongIdTable("tb_wise_invite_record", "id") {
    val userId = long("user_id")
    val inviteTgName = varchar("invite_tg_name", 128)
    val inviteCode = varchar("invite_code", 32)
    val eventStage = integer("event_stage")
    val inviteeId = long("invitee_id")
    val inviteAt = timestamp("invite_at")
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBCoinAndTokenPrice : LongIdTable("tb_coin_and_token_price", "id") {
    val tokenName = varchar("token_name", 32)
    val price = double("price")
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}

object TBTonSocietySyncPrivilege : LongIdTable("tb_ton_society_sync_privilege", "id") {
    val projectId = long("project_id")
    val campaignId = long("campaign_id")
    val status = integer("status").default(1)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBTonSyncHistory : LongIdTable("tb_ton_sync_history", "id") {
    val projectId = long("project_id")
    val campaignId = long("campaign_id")
    val groupId = long("group_id")
    val title = varchar("title", 255)
    val subtitle = varchar("subtitle", 512)
    val description = text("description")
    val startDate = timestamp("start_date")
    val endDate = timestamp("end_date")
    val buttonLabel = varchar("button_label", 255)
    val buttonLink = varchar("button_link", 255)
    val sbtCollectionTitle = varchar("sbt_collection_title", 255)
    val sbtCollectionDesc = varchar("sbt_collection_desc", 512)
    val sbtItemTitle = varchar("sbt_item_title", 255)
    val sbtImage = varchar("sbt_image", 255)
    val sbtVideo = varchar("sbt_video", 255)
    val sbtDesc = varchar("sbt_desc", 512)
    val syncAt = timestamp("sync_at")
    val activityId = long("activity_id")
    val activityUrl = varchar("activity_url", 255)
    val sbtId = long("sbt_id")
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBTonSyncCheck : LongIdTable("tb_ton_sync_check", "id") {
    val projectId = long("project_id")
    val campaignId = long("campaign_id")
    val groupId = long("group_id")
    val title = varchar("title", 255)
    val subtitle = varchar("subtitle", 512)
    val description = text("description")
    val startDate = timestamp("start_date")
    val endDate = timestamp("end_date")
    val buttonLabel = varchar("button_label", 255)
    val buttonLink = varchar("button_link", 255)
    val sbtCollectionTitle = varchar("sbt_collection_title", 255)
    val sbtCollectionDesc = varchar("sbt_collection_desc", 512)
    val sbtItemTitle = varchar("sbt_item_title", 255)
    val sbtImage = varchar("sbt_image", 255)
    val sbtVideo = varchar("sbt_video", 255)
    val sbtDesc = varchar("sbt_desc", 512)
    val syncAt = timestamp("sync_at")
    val activityId = long("activity_id")
    val activityUrl = varchar("activity_url", 255)
    val sbtId = long("sbt_id")
    val category = integer("category")
    val taskCategory = varchar("task_category", 128)
    val networkId = integer("network_id")
    val checkStatus = integer("check_status")
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBHomeBanner : LongIdTable("tb_home_banner", "id") {
    val imageUrl = varchar("image_url", 128)
    val link = varchar("link", 128)
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

object TBHomeCampaign : LongIdTable("tb_home_campaign", "id") {
    val campaignId = long("campaign_id")
    val campaignTitle = varchar("campaign_title", 128)
    val imageUrl = varchar("image_url", 128)
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

object TBHomeSBT : LongIdTable("tb_home_sbt", "id") {
    val sbtId = long("sbt_id")
    val sbtTitle = varchar("sbt_title", 128)
    val activityId = integer("activity_id")
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

object TBHomeProject : LongIdTable("tb_home_project", "id") {
    val projectId = long("project_id")
    val projectTitle = varchar("project_title", 128)
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

object TBHomeProjectTemplate : LongIdTable("tb_home_project_template", "id") {
    val templateName = varchar("template_name", 128)
    val bannerTitle = varchar("banner_title", 255)
    val bannerPicUrl = varchar("banner_pic_url", 255)
    val bannerSbtIds = text("banner_sbt_ids")
    val bannerSubTitle = varchar("banner_sub_title", 255)
    val middleTitle = varchar("middle_title", 255)
    val middleDescription = varchar("middle_description", 255)
    val tonHeroCampaignId = long("ton_hero_campaign_id")
    val tonHeroSbtPicUrl = varchar("ton_hero_sbt_pic_url", 255)
    val tonHeroTitle = varchar("ton_hero_title", 255)
    val tonHeroDescription = varchar("ton_hero_description", 255)
    val campaignIds = text("campaign_ids")
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

object TBLateNightAirdrop : LongIdTable("tb_latenight_airdrop_user", "id") {
    val userId = long("user_id")
    val tonAddress = varchar("ton_address", 128)
    val wiseScore = integer("wise_score").default(0)
    val walletAge = integer("wallet_age").default(0)
    val tonBalance = long("ton_balance").default(0)
    val transactionNum = integer("transaction_num").default(0)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBTgUserPush : LongIdTable("tb_tg_user_push", "id") {
    val tgPushSettingId = long("tg_push_setting_id")
    val tgId = long("tg_id")
    val userId = long("user_id")
    val pushType = varchar("push_type", 128)
    val participateType = integer("participate_type").default(0)
    val groupId = integer("group_id").default(0)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBTwitterUserCode : LongIdTable("tb_twitter_user_code", "id") {
    val userId = long("user_id")
    val userCode = varchar("user_code", 32)
    val status = integer("status").default(1)
    val tweetId = varchar("tweet_id", 128)
    val twitterId = varchar("twitter_id", 128)
    val twitterName = varchar("twitter_name", 255)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBUserCheckIn : LongIdTable("tb_wise_score_user_check_in", "id") {
    val userId = long("user_id") // 用户 ID
    val checkInDate = timestamp("check_in_date") // 打卡日期
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBUserStreaks : LongIdTable("tb_user_streaks", "id") {
    val userId = long("user_id")
    val currentStreak = integer("current_streak").default(0) // 当前连续打卡天数，默认为 0
    val maxStreak = integer("max_streak").default(0) // 历史最长连续打卡天数，默认为 0
    val maxStreakBeginDate = timestamp("max_streak_begin_date").nullable() // 历史最长连续打卡天数 - 起始日期
    val maxStreakEndDate = timestamp("max_streak_end_date").nullable() // 历史最长连续打卡天数 - 结束日期
    val lastCheckInDate = timestamp("last_check_in_date").nullable() // 最后一次手动打卡的日期
    val totalSignCardsUsed = integer("total_sign_cards_used").default(0) // 累计使用的补签卡数量
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBUserSignCard : LongIdTable("tb_user_sign_card", "id") {
    val userId = long("user_id") // 用户 ID
    val missedDate = timestamp("missed_date") // 被补签的日期
    val cardLevel = integer("card_level") // 补签卡的等级（对应不同的价格区间）
    val cardPrice = double("card_price") // 补签卡的价格（TON）
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBRetroactiveCardInventory : LongIdTable("tb_retroactive_card_inventory", "id") {
    val inventoryId = long("inventory_id") // 库存 ID
    val userId = long("user_id") // 用户 ID
    val totalCards = integer("total_cards") // 总补签卡数量
    val balanceCards = integer("balance_cards") // 剩余补签卡数量
    val usedCards = integer("used_cards") // 已使用补签卡数量
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBRetroactiveCardLedger : LongIdTable("tb_retroactive_card_ledger", "id") {
    val ledgerId = long("ledger_id") // 流水 ID
    val userId = long("user_id") // 用户 ID
    val outId = varchar("out_id", 255) // 唯一 ID
    val usedAmount = long("spend_amount") // 消费金额
    val addedAmount = long("added_amount") // 增加数量
    val fromAmount = long("from_amount") // 原有补签卡数量
    val toAmount = long("to_amount") // 剩余补签卡数量
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBStarInvoices : LongIdTable("tb_star_invoices", "id") {
    val invoiceId = long("invoice_id")
    val userId = long("user_id")
    val amount = integer("amount")
    val product = varchar("product", 255)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBStarPayments : LongIdTable("tb_start_payments", "id") {
    val paymentId = long("payment_id")
    val telegramChargeId = varchar("telegram_charge_id", 255)
    val userId = long("user_id")
    val invoiceId = long("invoice_id")
    val starAmount = integer("star_amount")
    val productAmount = integer("product_amount")
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
}

object TBAiAgent : LongIdTable("tb_ai_agent", "id") {
    val agentId = long("agent_id")
    val agentName = varchar("agent_name", 255)
    val agentImage = varchar("agent_image", 512)
    val bio = text("bio")
    val systemPrompt = text("system_prompt") // 系统提示
    val lore = text("lore") // 背景知识
    val status = integer("status") // 状态：0 - 默认，1 - 部署，2 - 停止

    // intelligence
    val knowledgeList = text("knowledge_list") // 知识点列表
    val topicsList = text("topics_list") // 话题列表

    // style setting
    val adjectives = text("adjectives") // 描述性词语列表
    val generalStyleRules = text("general_style_rules") // 通用风格规则

    // model setting
    val modelProvider = varchar("model_provider", 255) // 模型提供商
    val modelName = varchar("model_name", 255) // 模型名称
    val voiceModel = varchar("voice_model", 255) // 语音模型

    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBTgClientConfig : LongIdTable("tb_ai_tg_client_config", "id") {
    val tgClientConfigId = long("tg_client_config_id")
    val agentId = long("agent_id")
    val chatStyles = text("chat_styles") // 聊天风格
    val tgBotToken = varchar("tg_bot_token", 255) // Telegram 机器人 Token
    val conversationExamples = text("conversation_examples") // 会话示例，存为 JSON 格式

    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBAirDrop : LongIdTable("tb_air_drop", "id") {
    val airDropId = long("air_drop_id")
    val chainId = integer("chain_id")
    val chainName = varchar("chain_name", 255)
    val projectName = varchar("project_name", 255) // 项目名称
    val projectDesc = text("project_desc") // 项目描述
    val socialLink = varchar("social_link", 255).default("") // 社交链接
    val airDropDesc = text("air_drop_desc").default("") // 空投描述

    val tokenName = varchar("token_name", 255) // 代币名称
    val tokenContractAddress = varchar("token_contract_address", 255).default("") // 合约地址
    val airdropContractAddress = varchar("airdrop_contract_address", 255).default("") // 合约地址
    val status = integer("status").default(0) // 状态：0 - 未开始, 1 - 进行中, 2 - 已结束

    val gas = varchar("gas", 128)

    val startTime = timestamp("start_time").nullable() // 开始时间
    val endTime = timestamp("end_time").nullable() // 结束时间

    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBUserAirDropInfo : LongIdTable("tb_user_air_drop_info", "id") {
    val userId = long("user_id") // 用户 ID
    val chain = varchar("chain", 255).default("") // 链名称
    val address = varchar("address", 255).default("") // 钱包地址
    val airDropId = long("air_drop_id")
    val airDropTokenCnt = integer("air_drop_token_cnt").default(0)
    val claimedType = integer("claimed_type").default(0) // 领取状态：0 - 未领取, 1 - 领取中, 2 - 已领取

    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBTgUserPushSetting : LongIdTable("tb_tg_user_push_setting", "id") {
    val tgPushId = long("tg_push_id").default(0) // 推送 ID
    val pushType = varchar("push_type", 255).default("") // 推送类型
    val userType = integer("user_type").default(0) // 用户类型：0 - ALL USER, 1 - SBT holder, etc.
    val activityId = integer("activity_id").nullable() // 活动 ID，当 userType = 1 时才有值
    val topWiseScoreType = integer("top_wise_score_type").nullable() // top Wise score 类型
    val wiseScoreType = integer("wise_score_type").nullable() // Wise 分数条件类型
    val wiseScoreLimit = integer("wise_score_limit").nullable() // 分数限制
    val whiteList = text("white_list")
    val hasGroups = integer("has_groups").nullable() // 是否分组：0 - 不分组，1 - 分组
    val hasPushed = integer("has_pushed").default(0) // 推送状态：0 - 未推送，1 - 已推送
    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBPushBasicInfo : LongIdTable("tb_push_basic_info", "id") {
    val tgPushSettingId = long("tg_push_setting_id").references(TBTgUserPushSetting.id) // 外键，关联到主表
    val imgUrl = varchar("img_url", 512).default("") // 图片地址
    val content = text("content").default("") // 推送内容
    val buttonName = varchar("button_name", 255).default("") // 按钮名称
    val buttonUrl = varchar("button_url", 512).default("") // 按钮 URL
    val pushDate = timestamp("push_date").nullable() // 推送日期

    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBPushResultInfo : LongIdTable("tb_push_result_info", "id") {
    val tgPushSettingId = long("tg_push_setting_id").references(TBTgUserPushSetting.id) // 外键，关联到主表
    val groupId = integer("group_id").default(0)
    val totalPushCnt = integer("total_push_cnt").default(0) // 总推送数
    val successPushCnt = integer("success_push_cnt").default(0) // 推送成功数
    val failedPushCnt = integer("failed_push_cnt").default(0) // 推送失败数

    val createTime = timestamp("created_at") // 创建时间
    val updateTime = timestamp("updated_at") // 更新时间
}

object TBUserSui : LongIdTable("tb_user_sui", "id") {
    val userId = long("user_id").uniqueIndex()
    val address = varchar("address", 255)
    val publicKey = varchar("public_key", 255)
    val createTime = timestamp("created_at")
    val updateTime = timestamp("updated_at")
}
